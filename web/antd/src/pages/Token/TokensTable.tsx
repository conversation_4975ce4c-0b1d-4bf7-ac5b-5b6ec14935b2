import React, {lazy, useContext, useEffect, useState} from 'react'
import {
    API,
    copy,
    getChatLinkCompatibleWithOldFormats,
    getShellGPTChatLinkWithToken,
    isMobile,
    showError,
    timestamp2string,
} from '../../helpers'
import {useNavigate} from 'react-router-dom'
import {createRenderFunctions} from '../../helpers/render'

import {App, Button, Dropdown, Input, Modal, Popconfirm, Popover, Select, Space, Tag} from 'antd'
import {
    CheckCircleTwoTone,
    CodeTwoTone,
    CopyTwoTone,
    DeleteFilled,
    DeleteTwoTone,
    DiffFilled,
    DollarTwoTone,
    EditFilled,
    EditTwoTone,
    FileExcelFilled,
    FileTextTwoTone,
    GiftTwoTone,
    InteractionTwoTone,
    LoadingOutlined,
    LockFilled,
    MessageTwoTone,
    PlayCircleTwoTone,
    SettingTwoTone,
    StopTwoTone,
    ToolTwoTone
} from '@ant-design/icons'
import {isUrl, ProTable} from '@ant-design/pro-components'
import Papa from 'papaparse'
import {
    actionButtonProps,
    DefaultResponse,
    modalProps,
    modalPropsWithFooter,
    paginationProps,
    SYSTEM_NAME
} from '../../constants';
import {getCountByPathAndParam, getDataByPathAndParam, getDefaultToken} from '../../helpers/api-request-module'
import {StatusContext} from "../../context/Status";
import {AxiosResponse} from 'axios'
import {UserContext} from "../../context/User";
import {useGroup} from "../../context/Group";
import AddQuotaModal from './AddQuotaModal';
import {useTranslation} from "react-i18next";
import {useActionRef} from "../../hooks/useActionRef";

const RefreshTokenModal = lazy(() => import('./RefreshTokenModal'))
const CodeExampleModal = lazy(() => import('./CodeExampleModal'))
const EditTokenFormModal = lazy(() => import('./EditTokenFormModal'))

const {Option} = Select

// 在文件顶部添加这个接口定义
interface GroupOption {
    label: string;
    value: string;
    key: number;
    convert_ratio?: number;
    current_group_ratio?: number;
    current_topup_ratio?: number;
}

const TokensTable = () => {
    const {t} = useTranslation();
    const { renderModels, renderQuota, renderTokenStatus,renderTokensBillingTypeTag } = createRenderFunctions(t);

    const {message: AntdMessage, modal} = App.useApp();
    const [statusState] = useContext(StatusContext);
    const [userState] = useContext(UserContext);
    const {groups, loading: groupsLoading, fetchGroups} = useGroup();
    
    const isRoot = userState.user.role === 100
    // 导出
    const [tokens, setTokens] = useState([])
    const [downloading, setDownLoading] = useState(false)

    // 对接
    const [isBridgeModalVisible, setIsBridgeModalVisible] = useState(false)
    const [preCopyTokenInfo, setPreCopyTokenInfo] = useState<any>({}) // 用于对接的令牌
    const [userAvailableModels, setUserAvailableModels] = useState([])//储存用户可用模型，用于填入
    const [bridgeServerAddress, setBridgeServerAddress] = useState('') // 用户输入的服务器地址

    // 手动复制
    const [isCopyTokenModalVisible, setIsCopyTokenModalVisible] = useState(false)
    const [manuallyCopyToken, setManuallyCopyToken] = useState('')

    // 编辑
    const [isTokenEditModalVisible, setIsTokenEditModalVisible] = useState(false)
    const [isRefreshTokenConfirmModalVisible, setIsRefreshTokenConfirmModalVisible] = useState(false)
    const [refreshTokenText, setRefreshTokenText] = useState('')
    const [defaultTokenRecordId, setDefaultTokenRecordId] = useState(0)
    const [editingTokenId, setEditingTokenId] = useState(0)

    // 批量操作
    const [isEditing, setIsEditing] = useState(false)
    const [selectedRowKeys, setSelectedRowKeys] = useState([] as number[])

    // 代码示例
    const [isCodeExampleModalVisible, setIsCodeExampleModalVisible] = useState(false)//是否展示代码示例模态框
    const [apiToken, setApiToken] = useState('sk-xxx') // 用于示例代码的 apiToken
    const [codeExampleAvailableModels, setCodeExampleAvailableModels] = useState<string[]>([]) // 代码示例的可用模型

    // 分页与数据
    const [tokensCount, setTokensCount] = useState<number>(0)
    const [showCopySuccessfullyIds, setShowCopySuccessfullyIds] = useState([] as number[])
    const [isModalAddQuotaVisible, setIsModalAddQuotaVisible] = useState(false);
    const [editingUserIdForBalance, setEditingUserIdForBalance] = useState(0);//当前编辑的用户 ID，用于模态框（编辑用户余额）

    useEffect(() => {
        fetchGroups();
    }, []);

    // 表格刷新
    const { ref, safeAction } = useActionRef();

    const showAddQuotaModal = (tokenId: number) => {
        setEditingUserIdForBalance(tokenId);
        setIsModalAddQuotaVisible(true);
    };

    // 获取用户可用模型
    async function fetchUserAvailableModels(tokenGroup?: string) {
        try {
            const response = await API.get('/api/user/available_models_by_groups');
            if (response.data.success) {
                const { groups, userGroup } = response.data.data;

                // 使用传入的令牌分组，如果没有则使用用户默认分组
                const targetGroup = tokenGroup || userGroup;

                // 查找对应分组的模型列表
                const targetGroupData = groups.find(g => g.name === targetGroup);

                if (targetGroupData) {
                    setUserAvailableModels(targetGroupData.models);
                } else {
                    // 如果找不到对应分组，使用空数组
                    setUserAvailableModels([]);
                    AntdMessage.warning(t('tokensTable.warning.noModelsInGroup'));
                }
            } else {
                AntdMessage.error(t('tokensTable.error.fetchModelsFailed', { message: response.data.message }));
            }
        } catch (error) {
            AntdMessage.error(t('tokensTable.error.fetchModelsFailed', { message: '' }));
        }
    }

    // 获取令牌的可用模型（用于代码示例）
    async function fetchTokenAvailableModels(record: any) {
        try {
            // 使用 /v1/models 接口获取该令牌的实际可用模型
            const response = await API.get('/v1/models', {
                headers: {
                    'Authorization': `Bearer sk-${record.key}`
                }
            });

            if (response.data && response.data.data) {
                // 提取模型ID列表
                const models = response.data.data.map((model: any) => model.id).filter(Boolean);
                setCodeExampleAvailableModels(models);
            } else {
                setCodeExampleAvailableModels([]);
            }
        } catch (error) {
            console.error('获取令牌可用模型失败:', error);
            // 如果获取失败，使用空数组，让组件使用默认的静态模型列表
            setCodeExampleAvailableModels([]);
        }
    }



    // 打开编辑模态框
    const handleEditToken = (TokenId: number) => {
        setEditingTokenId(TokenId) // 设置当前编辑的令牌 ID
        setIsTokenEditModalVisible(true) // 显示模态框
    };

    // 批量删除
    const deleteTokensByIds = async (ids: number[]) => {
        try {
            const res = await API.delete(`/api/token/deleteByIds`, {data: ids,})
            const {data, success, message} = res.data
            if (success) {
                AntdMessage.success(t('tokensTable.success.batchDelete', { count: data }))
            } else {
                AntdMessage.error(t('tokensTable.error.batchDeleteFailed', { message }))
            }
        } catch (error) {
            showError(error)
        } finally {
            setIsEditing(false)
            setSelectedRowKeys([])
            safeAction('reload');
        }
    };

    // 下拉菜单
    const CustomDropdown = ({record, chatLinkArr}) => {
        const navigate = useNavigate();
        const onClick = async ({key, domEvent}) => {
            domEvent.stopPropagation(); // 防止事件冒泡
            switch (key) {
                case '92':
                    const action = record.status === 1 ? 'disable' : 'enable';
                    await manageToken(record.id, action);
                    break;
                case '93':
                    handleEditToken(record.id);
                    break;
                case '94':
                    setIsCodeExampleModalVisible(true)
                    setApiToken(record.key)
                    // 获取该令牌的可用模型
                    await fetchTokenAvailableModels(record)
                    break;
                case '95':
                    navigate(`/log?current=1&pageSize=50&token_key=sk-${record.key}`)
                    break;
                case '96':
                    await onCopy('share', record);
                    break;
                case '97':
                    await handleOpenBridgeModal(record);
                    break;
                default:
                    console.log(`Clicked on item ${key},which is not handled`);
                    break;
            }
        };

        const items = [
            ...(chatLinkArr?.map((chatLink: { type: number,route:string|undefined }, index: number) => ({
                label: <a href={`${getShellGPTChatLinkWithToken(record.key, chatLink.type,chatLink.route)}`} target="_blank"
                          rel="noreferrer">{chatLinkArr[index].label ?? t('tokensTable.dropdown.onlineChat')} </a>,
                key: `${index + 1}`,
                icon: <MessageTwoTone/>,
            })) ?? []),
            {
                label: record.status === 1 ? t('tokensTable.dropdown.disableToken') : t('tokensTable.dropdown.enableToken'),
                key: '92',
                icon: record.status === 1 ? <StopTwoTone/> : <PlayCircleTwoTone/>,
            },
            {
                label: t('tokensTable.dropdown.editToken'),
                key: '93',
                icon: <EditTwoTone/>,
            },
            {
                label: t('tokensTable.dropdown.requestExample'),
                key: '94',
                icon: <CodeTwoTone/>,
            },
            {
                label: t('tokensTable.dropdown.tokenLog'),
                key: '95',
                icon: <FileTextTwoTone/>,
            },
            {
                label: t('tokensTable.dropdown.shareToken'),
                key: '96',
                icon: <GiftTwoTone/>,
            },
            {
                label: t('tokensTable.dropdown.quickIntegration'),
                key: '97',
                icon: <SettingTwoTone/>,
            },
        ].filter(Boolean); // 过滤掉可能的null值

        return (
            <Dropdown menu={{items, onClick}}>
                <Button {...actionButtonProps} icon={<ToolTwoTone/>}/>
            </Dropdown>
        );
    };

    // 复制
    const onCopy = async (type: string, record: any) => {
        let toCopy: string = '';
        let successMessage: string = '';

        switch (type) {
            case 'share':
                try {
                    let models: string[] = [];

                    // 优先使用 /v1/models 接口获取该令牌的实际可用模型
                    try {
                        const response = await API.get('/v1/models', {
                            headers: {
                                'Authorization': `Bearer sk-${record.key}`
                            }
                        });

                        if (response.data && response.data.data) {
                            models = response.data.data.map((model: any) => model.id).filter(Boolean);
                            console.log('从 /v1/models 获取到的模型:', models);
                        }
                    } catch (modelsError) {
                        console.log('无法从 /v1/models 获取模型，尝试使用分组模型:', modelsError);

                        // 如果 /v1/models 失败，回退到原有的分组逻辑
                        if (record.models) {
                            // 如果令牌有直接配置的模型，使用它们
                            models = record.models.split(',').filter(Boolean);
                            console.log('使用令牌配置的模型:', models);
                        } else {
                            // 否则根据分组获取模型
                            const res = await API.get('/api/user/available_models_by_groups');
                            const groups = res.data.data?.groups || [];
                            const tokenGroup = record.group || record.group_name;
                            const targetGroupData = groups.find(g => g.name === tokenGroup);

                            if (targetGroupData && targetGroupData.models) {
                                models = targetGroupData.models;
                                console.log('使用分组模型:', models);
                            } else {
                                console.log('未找到可用模型');
                                AntdMessage.warning(t('tokensTable.warning.noModelsInGroup'));
                            }
                        }
                    }

                    // 格式化为英文逗号分隔
                    const formattedModels = models.join(', ');

                    toCopy = statusState.customConfig.CustomShareText
                        .replace('{key}', 'sk-' + record.key)
                        .replace(
                            '{balance}',
                            record.unlimited_quota
                                ? t('tokensTable.export.unlimited')
                                : (record.remain_quota / 500000).toFixed(2)
                        )
                        .replace('{models}', formattedModels)
                        .replace('{base_url}', statusState.status.server_address);
                    successMessage = t('tokensTable.success.shareTextCopied');
                } catch (error) {
                    console.error('获取模型失败:', error);
                    const errorMessage = error instanceof Error ? error.message : '未知错误';
                    AntdMessage.error(t('tokensTable.error.modelFetchFailed', { message: errorMessage }));
                    return;
                }
                break;
            case 'token_key':
                if (showCopySuccessfullyIds.includes(record.id)) return;
                toCopy = `sk-${record.key}`;
                successMessage = t('tokensTable.success.tokenCopied');
                break;
            default:
                return;
        }

        if (await copy(toCopy)) {
            AntdMessage.success({content: successMessage, duration: 2, key: 'copy'});
            if (type === 'token_key') {
                setShowCopySuccessfullyIds(prevIds => [...prevIds, record.id]);
            }
        } else {
            AntdMessage.warning({content: t('tokensTable.warning.copyFailed'), duration: 2, key: 'copy'});
            setManuallyCopyToken(toCopy);
            setIsCopyTokenModalVisible(true);
        }
    };

    // 操作
    const manageToken = async (id: any, action: "delete" | "refresh" | "enable" | "disable") => {
        let data = {}
        let res = {} as AxiosResponse
        switch (action) {
            case 'delete':
                res = await API.delete(`/api/token/${id}/`)
                break
            case 'refresh':
                res = await API.delete(`/api/token/${id}/`)
                if (res.data.success) {
                    setRefreshTokenText('')
                    setIsRefreshTokenConfirmModalVisible(false)
                    await getDefaultToken()
                }
                break
            case 'enable':
                data = {id, status: 1}
                res = await API.put('/api/token/?status_only=true', data)
                break
            case 'disable':
                data = {id, status: 2}
                res = await API.put('/api/token/?status_only=true', data)
                break
            default:
                break
        }
        if (res.data.success) {
            safeAction('reload');
            AntdMessage.success(t(`tokensTable.success.${action}Token`))
        } else {
            AntdMessage.error(t(`tokensTable.error.${action}TokenFailed`, { message: res.data.message }))
        }
    };

    // 导出
    const downloadCSV = (event: any) => {
        event.stopPropagation()
        if (!tokens.length || downloading) return;
        setDownLoading(true)
        const filteredData = tokens.map((row: any) => {
            const rowObj = {}
            rowObj[t('tokensTable.export.name')] = row.name
            rowObj[t('tokensTable.export.key')] = 'sk-' + row.key
            rowObj[t('tokensTable.export.billingType')] = t(`tokensTable.billingType.${row.billing_type}`)
            rowObj[t('tokensTable.export.status')] = t(`tokenStatus.${row.status}`)
            rowObj[t('tokensTable.export.models')] = row.models
            rowObj[t('tokensTable.export.usedQuota')] = '$' + row.used_quota / 500000
            rowObj[t('tokensTable.export.remainQuota')] = row.unlimited_quota ? t('tokensTable.export.unlimited') : '$' + row.remain_quota / 500000
            rowObj[t('tokensTable.export.createdTime')] = timestamp2string(row.created_time)
            rowObj[t('tokensTable.export.expiredTime')] = row.expired_time === -1 ? t('tokensTable.export.neverExpire') : timestamp2string(row.expired_time)
            return rowObj
        })

        const csv = Papa.unparse(filteredData)
        const blob = new Blob([csv], {type: 'text/csv'})
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'tokens.csv'
        a.click()
        AntdMessage.destroy()
        setDownLoading(false)
        AntdMessage.success(t('tokensTable.success.export')).then()
    };

    // 对接前输入服务器地址的模态框
    const handleOpenBridgeModal = async (record: any) => {
        setPreCopyTokenInfo(record);
        setIsBridgeModalVisible(true);

        if (!record.models) {
            await fetchUserAvailableModels(record.group);
        } else {
            setUserAvailableModels(record.models.split(','));
        }
    };

    // 准备跳转到对接页面
    const handleBridge = async () => {
        if (!bridgeServerAddress || !isUrl(bridgeServerAddress)) {
            AntdMessage.warning(t('tokensTable.warning.invalidServerAddress'))
            return
        }
        let token = `sk-${preCopyTokenInfo.key}`
        let billing_type = preCopyTokenInfo.billing_type
        await copy(token)
        const modelsParam = userAvailableModels.join(',')
        const bridgeUrl = `${bridgeServerAddress}/channel/bridge?name=${encodeURIComponent(statusState.status.system_name + t('tokensTable.bridge.quickIntegration'))}&base_url=${encodeURIComponent(statusState.status.server_address)}&model=${encodeURIComponent(modelsParam)}&billing_type=${billing_type}&type=7007`
        setIsBridgeModalVisible(false)
        AntdMessage.info(t('tokensTable.info.openingBridgePage'))
        window.open(bridgeUrl, '_blank')
    };

    // 表格列定义
    const columns = [
        {
            title: t('tokensTable.modal.field.name'),
            dataIndex: 'name',
            width: '15%',
            fixed: !isMobile(),
            render: (text: string, index: any) => (
                index.remark ?
                    <Popover placement="right" content={`${t('tokensTable.modal.field.description')}: ${index.remark}`}>
                        <span>{text}</span>
                    </Popover> : <span>{text}</span>
            ),
        },
        {
            title: t('logsTable.tokenGroup'),
            width: '15.5%',
            dataIndex: 'group',
            render: (token_group) => {
                if (!token_group) return '';
                
                if (groups.length === 0) {
                    return (
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <span>{token_group}</span>
                            {groupsLoading && <LoadingOutlined style={{ fontSize: 14 }} />}
                        </div>
                    );
                }

                const group = groups.find(g => g.name === token_group);
                if (!group) return token_group;
                const isCurrentGroup = group.name === userState.user.group;
                const displayRatio = isCurrentGroup
                    ? group.current_group_ratio
                    : group.convert_ratio * (group.current_group_ratio || 1) * (group.current_topup_ratio || 1);
                              
                return (
                    <div style={{ 
                        display: 'flex', 
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        width: '100%',
                        gap: '8px'
                    }}>
                        <span style={{ 
                            flex: 1,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                        }}>
                            {group.display_name}
                        </span>
                        <span style={{ 
                            padding: '2px 8px',
                            borderRadius: '4px',
                            fontSize: '12px',
                            backgroundColor: displayRatio > 1 ? 'rgba(255, 77, 79, 0.1)' : 'rgba(82, 196, 26, 0.1)',
                            color: displayRatio > 1 ? '#ff4d4f' : '#52c41a',
                            flexShrink: 0
                        }}>
                            {displayRatio.toFixed(1)}x
                        </span>
                    </div>
                );
            },
            renderFormItem: (_item, {defaultRender, ...rest}) => {
                return <Select
                    {...rest}
                    placeholder={t('logsTable.selectGroup')}
                    style={{ width: '100%' }}
                    loading={groupsLoading}
                    options={groups.map(group => {
                        const isCurrentGroup = group.name === userState.user.group;
                        const displayRatio = isCurrentGroup 
                            ? group.current_group_ratio 
                            : group.convert_ratio * (group.current_group_ratio || 1) * (group.current_topup_ratio || 1);
                        return {
                            label: (
                                <div style={{ 
                                    display: 'flex', 
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    width: '100%',
                                    gap: '8px'
                                }}>
                                    <span style={{ 
                                        flex: 1,
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        whiteSpace: 'nowrap'
                                    }}>
                                        {group.display_name}
                                    </span>
                                    <span style={{ 
                                        padding: '2px 8px',
                                        borderRadius: '4px',
                                        fontSize: '12px',
                                        backgroundColor: displayRatio > 1 ? 'rgba(255, 77, 79, 0.1)' : 'rgba(82, 196, 26, 0.1)',
                                        color: displayRatio > 1 ? '#ff4d4f' : '#52c41a',
                                        flexShrink: 0
                                    }}>
                                        {displayRatio.toFixed(1)}x
                                    </span>
                                </div>
                            ),
                            value: group.name
                        };
                    })}
                />;
            }
        },
        {
            title: t('tokensTable.modal.field.type.default'),
            width: '10%',
            dataIndex: 'billing_type',
            renderFormItem: (_: any, {defaultRender, ...rest}: any,) => {
                return <Select {...rest} placeholder={t('tokensTable.modal.field.type.default')} allowClear>
                    <Option value="1">{t('tokensTable.modal.field.type.type1')}</Option>
                    <Option value="2">{t('tokensTable.modal.field.type.type2')}</Option>
                    <Option value="3">{t('tokensTable.modal.field.type.type3')}</Option>
                    <Option value="4">{t('tokensTable.modal.field.type.type4')}</Option>
                    <Option value="5">{t('tokensTable.modal.field.type.type5')}</Option>
                </Select>
            },
            render: (_text: string, record: any) => renderTokensBillingTypeTag(record.billing_type),
        },
        {
            title: t('tokensTable.modal.field.status'),
            dataIndex: 'status',
            renderFormItem: (_: any, {defaultRender, ...rest}: any,) => {
                return <Select {...rest} placeholder={t('tokensTable.modal.field.status')} allowClear>
                    <Option value="1">{t('tokensTable.modal.field.statusEnabled')}</Option>
                    <Option value="2">{t('tokensTable.modal.field.statusDisabled')}</Option>
                    <Option value="3">{t('tokensTable.modal.field.statusExpired')}</Option>
                    <Option value="4">{t('tokensTable.modal.field.statusExhausted')}</Option>
                </Select>
            },
            render: (_text: string, record: any) => renderTokenStatus(record.status),
            width: '6%',
        },
        {
            title: t('tokensTable.modal.field.models'),
            search: false,
            dataIndex: 'models',
            render: (_text: string, record: any) => renderModels(record.models, 3, 2),
            width: '20%',
        },
        {
            title: t('tokensTable.modal.field.usedQuota'),
            search: false,
            dataIndex: 'used_quota',
            render: (_text: string, record: any) => renderQuota(record.used_quota),
            width: '10%',
        },
        {
            title: t('tokensTable.modal.field.remainQuota'),
            search: false,
            dataIndex: 'remain_quota',
            render: (_text: string, record: any) => record.unlimited_quota ? t('tokensTable.modal.field.unlimited') : renderQuota(record.remain_quota, 2),
            width: '10%',
        },
        {
            title: t('tokensTable.modal.field.createdTime'),
            search: false,
            dataIndex: 'created_time',
            render: (_text: string, record: any) => timestamp2string(record.created_time, true),
            width: '12.5%',
            hideInTable: isMobile(),
        },
        {
            title: t('tokensTable.modal.field.expiredTime'),
            search: false,
            dataIndex: 'expired_time',
            render: (_text: string, record: any) => record.expired_time === -1 ? t('tokensTable.modal.field.neverExpire') : timestamp2string(record.expired_time, true),
            width: '12.5%',
            hideInTable: isMobile(),
        },
        {
            title: t('tokensTable.table.action'),
            width: '15%',
            fixed: isMobile() ? false : 'right',
            search: false,
            key: 'action',
            render: (_text: any, record: any) => (
                <Space size="small">
                    {userState.user.role >= 100 &&
                        <Button
                            {...actionButtonProps}
                            icon={<DollarTwoTone/>}
                            onClick={() => showAddQuotaModal(record.id)}
                        />
                    }
                    <Button
                        {...actionButtonProps}
                        icon={showCopySuccessfullyIds.includes(record.id) ? <CheckCircleTwoTone/> : <CopyTwoTone/>}
                        onClick={async () => {
                            await onCopy('token_key', record)
                            setTimeout(() => {
                                setShowCopySuccessfullyIds(prevIds => prevIds.filter(id => id !== record.id))
                            }, 1500)
                        }}
                    />
                    {record.is_initial_token ?
                        <Button
                            {...actionButtonProps}
                            icon={<InteractionTwoTone/>}
                            onClick={() => {
                                setIsRefreshTokenConfirmModalVisible(true)
                                setDefaultTokenRecordId(record.id)
                            }}
                        />
                        :
                        <Button
                            {...actionButtonProps}
                            icon={<DeleteTwoTone/>}
                            onClick={async () => {
                                modal.confirm({
                                    ...modalPropsWithFooter,
                                    title: t('tokensTable.modal.delete.title'),
                                    content: t('tokensTable.modal.delete.content', {name: record.name}),
                                    onOk: async () => {
                                        await manageToken(record.id, 'delete')
                                    }
                                })
                            }}
                        />
                    }

                    <CustomDropdown record={record} chatLinkArr={getChatLinkCompatibleWithOldFormats()}/>
                </Space>
            ),
        },
        {
            title: t('tokensTable.modal.field.key'),
            dataIndex: 'key',
            hideInTable: true,
        },
    ];
    // 表格列定义
    const columnsRoot = [
        {
            title: t('tokensTable.modal.field.name'),
            dataIndex: 'name',
            width: '15%',
            fixed: !isMobile(),
            render: (text: string, index: any) => (
                index.remark ?
                    <Popover placement="right" content={`${t('tokensTable.modal.field.description')}: ${index.remark}`}>
                        <span>{text}</span>
                    </Popover> : <span>{text}</span>
            ),
        },
        {
            title: t('tokensTable.modal.field.userId', '用户ID'),
            dataIndex: 'user_id',
            width: '5%',
        },
        {
            title: t('logsTable.tokenGroup'),
            width: '15.5%',
            dataIndex: 'group',
            render: (token_group) => {
                if (!token_group) return '';
                
                if (groups.length === 0) {
                    return (
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <span>{token_group}</span>
                            {groupsLoading && <LoadingOutlined style={{ fontSize: 14 }} />}
                        </div>
                    );
                }

                const group = groups.find(g => g.name === token_group);
                if (!group) return token_group;
                const isCurrentGroup = group.name === userState.user.group;
                const displayRatio = isCurrentGroup
                    ? group.current_group_ratio
                    : group.convert_ratio * (group.current_group_ratio || 1) * (group.current_topup_ratio || 1);

                return (
                    <div style={{ 
                        display: 'flex', 
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        width: '100%',
                        gap: '8px'
                    }}>
                        <span style={{ 
                            flex: 1,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                        }}>
                            {group.display_name}
                        </span>
                        <span style={{ 
                            padding: '2px 8px',
                            borderRadius: '4px',
                            fontSize: '12px',
                            backgroundColor: displayRatio > 1 ? 'rgba(255, 77, 79, 0.1)' : 'rgba(82, 196, 26, 0.1)',
                            color: displayRatio > 1 ? '#ff4d4f' : '#52c41a',
                            flexShrink: 0
                        }}>
                            {displayRatio.toFixed(1)}x
                        </span>
                    </div>
                );
            },
            renderFormItem: (_item, {defaultRender, ...rest}) => {
                return <Select
                    {...rest}
                    placeholder={t('logsTable.selectGroup')}
                    style={{ width: '100%' }}
                    loading={groupsLoading}
                    options={groups.map(group => {
                        const isCurrentGroup = group.name === userState.user.group;
                        const displayRatio = isCurrentGroup 
                            ? group.current_group_ratio 
                            : group.convert_ratio * (group.current_group_ratio || 1) * (group.current_topup_ratio || 1);
                        return {
                            label: (
                                <div style={{ 
                                    display: 'flex', 
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    width: '100%',
                                    gap: '8px'
                                }}>
                                    <span style={{ 
                                        flex: 1,
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        whiteSpace: 'nowrap'
                                    }}>
                                        {group.display_name}
                                    </span>
                                    <span style={{ 
                                        padding: '2px 8px',
                                        borderRadius: '4px',
                                        fontSize: '12px',
                                        backgroundColor: displayRatio > 1 ? 'rgba(255, 77, 79, 0.1)' : 'rgba(82, 196, 26, 0.1)',
                                        color: displayRatio > 1 ? '#ff4d4f' : '#52c41a',
                                        flexShrink: 0
                                    }}>
                                        {displayRatio.toFixed(1)}x
                                    </span>
                                </div>
                            ),
                            value: group.name
                        };
                    })}
                />;
            }
        },
        {
            title: t('tokensTable.modal.field.type.default'),
            width: '10%',
            dataIndex: 'billing_type',
            renderFormItem: (_: any, {defaultRender, ...rest}: any,) => {
                return <Select {...rest} placeholder={t('tokensTable.modal.field.type.default')} allowClear>
                    <Option value="1">{t('tokensTable.modal.field.type.type1')}</Option>
                    <Option value="2">{t('tokensTable.modal.field.type.type2')}</Option>
                    <Option value="3">{t('tokensTable.modal.field.type.type3')}</Option>
                    <Option value="4">{t('tokensTable.modal.field.type.type4')}</Option>
                    <Option value="5">{t('tokensTable.modal.field.type.type5')}</Option>
                </Select>
            },
            render: (_text: string, record: any) => renderTokensBillingTypeTag(record.billing_type),
        },
        {
            title: t('tokensTable.modal.field.status'),
            dataIndex: 'status',
            renderFormItem: (_: any, {defaultRender, ...rest}: any,) => {
                return <Select {...rest} placeholder={t('tokensTable.modal.field.status')} allowClear>
                    <Option value="1">{t('tokensTable.modal.field.statusEnabled')}</Option>
                    <Option value="2">{t('tokensTable.modal.field.statusDisabled')}</Option>
                    <Option value="3">{t('tokensTable.modal.field.statusExpired')}</Option>
                    <Option value="4">{t('tokensTable.modal.field.statusExhausted')}</Option>
                </Select>
            },
            render: (_text: string, record: any) => renderTokenStatus(record.status),
            width: '6%',
        },
        {
            title: t('tokensTable.modal.field.models'),
            search: false,
            dataIndex: 'models',
            render: (_text: string, record: any) => renderModels(record.models, 3, 2),
            width: '20%',
        },
        {
            title: t('tokensTable.modal.field.usedQuota'),
            search: false,
            dataIndex: 'used_quota',
            render: (_text: string, record: any) => renderQuota(record.used_quota),
            width: '10%',
        },
        {
            title: t('tokensTable.modal.field.remainQuota'),
            search: false,
            dataIndex: 'remain_quota',
            render: (_text: string, record: any) => record.unlimited_quota ? t('tokensTable.modal.field.unlimited', '无限制') : renderQuota(record.remain_quota, 2),
            width: '10%',
        },
        {
            title: t('tokensTable.modal.field.createdTime'),
            search: false,
            dataIndex: 'created_time',
            render: (_text: string, record: any) => timestamp2string(record.created_time, true),
            width: '12.5%',
            hideInTable: isMobile(),
        },
        {
            title: t('tokensTable.modal.field.expiredTime'),
            search: false,
            dataIndex: 'expired_time',
            render: (_text: string, record: any) => record.expired_time === -1 ? t('tokensTable.modal.field.neverExpire', '永不过期') : timestamp2string(record.expired_time, true),
            width: '12.5%',
            hideInTable: isMobile(),
        },
        {
            title: t('tokensTable.table.action', '操作'),
            width: '15%',
            fixed: isMobile() ? false : 'right',
            search: false,
            key: 'action',
            render: (_text: any, record: any) => (
                <Space size="small">
                    {userState.user.role >= 100 &&
                        <Button
                            {...actionButtonProps}
                            icon={<DollarTwoTone/>}
                            onClick={() => showAddQuotaModal(record.id)}
                        />
                    }
                    <Button
                        {...actionButtonProps}
                        icon={showCopySuccessfullyIds.includes(record.id) ? <CheckCircleTwoTone/> : <CopyTwoTone/>}
                        onClick={async () => {
                            await onCopy('token_key', record)
                            setTimeout(() => {
                                setShowCopySuccessfullyIds(prevIds => prevIds.filter(id => id !== record.id))
                            }, 1500)
                        }}
                    />
                    {record.is_initial_token ?
                        <Button
                            {...actionButtonProps}
                            icon={<InteractionTwoTone/>}
                            onClick={() => {
                                setIsRefreshTokenConfirmModalVisible(true)
                                setDefaultTokenRecordId(record.id)
                            }}
                        />
                        :
                        <Button
                            {...actionButtonProps}
                            icon={<DeleteTwoTone/>}
                            onClick={async () => {
                                modal.confirm({
                                    ...modalPropsWithFooter,
                                    title: t('tokensTable.modal.delete.title', '删除'),
                                    content: t('tokensTable.modal.delete.content', {name: record.name}),
                                    onOk: async () => {
                                        await manageToken(record.id, 'delete')
                                    }
                                })
                            }}
                        />
                    }

                    <CustomDropdown record={record} chatLinkArr={getChatLinkCompatibleWithOldFormats()}/>
                </Space>
            ),
        },
        {
            title: t('tokensTable.modal.field.key', '密钥'),
            dataIndex: 'key',
            hideInTable: true,
        },
    ];

    // 请求数据
    const request = async (params = {}) => {
        try {
            const [res, count] = await Promise.all([getDataByPathAndParam('token', params), getCountByPathAndParam('token', params)]);
            const {success, data, message} = res as DefaultResponse;
            if (!success) {
                AntdMessage.error(t('tokensTable.error.fetchDataFailed', { message }));
                return {success: false, data: [], total: 0};
            }
            setTokens(data);
            setTokensCount(count);
            return {success: success && count !== -1, data, total: count};
        } catch (error) {
            showError(error as Error);
        }
    };

    return (
        <>
            <ProTable
                rowKey="id"
                cardBordered
                columns={isRoot ? columnsRoot : columns as any}
                actionRef={ref as any}
                rowSelection={isEditing && {
                    selectedRowKeys: selectedRowKeys,
                    onChange: setSelectedRowKeys,
                } as any}
                scroll={{x: 1100}}
                headerTitle={t('tokensTable.title')}
                request={request as any}
                search={{labelWidth: 'auto', span: isMobile() ? undefined : 6}}
                toolBarRender={() => [
                    <>
                        {selectedRowKeys.length > 0 &&
                            <Popconfirm title={t('tokensTable.table.toolBar.delete')}
                                        description={t('tokensTable.table.toolBar.deleteConfirm', {count: selectedRowKeys.length})}
                                        onConfirm={async () => {
                                            await deleteTokensByIds(selectedRowKeys)
                                        }}>
                                <Button danger type={'dashed'}
                                        icon={<DeleteFilled/>}>{t('buttonText.delete')}</Button>
                            </Popconfirm>
                        }

                        <Button icon={<EditFilled/>} type={isEditing ? 'dashed' : 'default'}
                                onClick={() => {
                                    if (isEditing) {
                                        setIsEditing(false)
                                        setSelectedRowKeys([])
                                    } else {
                                        setIsEditing(true)
                                    }
                                }}
                        >
                            {isEditing ? t('buttonText.cancel') : t('buttonText.multiple')}
                        </Button>

                        <Popconfirm title={t('tokensTable.table.toolBar.exportConfirm')} onConfirm={downloadCSV}>
                            <Button icon={<FileExcelFilled/>} loading={downloading}>{t('tokensTable.table.toolBar.export')}</Button>
                        </Popconfirm>
                        <Button type="primary" icon={<DiffFilled/>} onClick={() => handleEditToken(-1)}>{t('buttonText.add')}</Button>
                    </>,
                ]}
                tableAlertRender={false}
                tableAlertOptionRender={false}
                defaultSize="small"
                pagination={{
                    ...paginationProps,
                    total: tokensCount,
                }}
            />

            <Modal
                getContainer={() => document.getElementById('globalModalContainer') as HTMLElement}
                open={isBridgeModalVisible}
                onCancel={() => setIsBridgeModalVisible(false)}
                title={t('tokensTable.modal.bridge.title')}
                footer={<Button onClick={handleBridge} type="primary">{t('buttonText.confirm')}</Button>}
            >
                <Input
                    style={{marginTop: '20px'}}
                    name="yourdomain"
                    placeholder={t('tokensTable.modal.bridge.placeholder', {name: SYSTEM_NAME})}
                    onChange={(e) => setBridgeServerAddress(e.target.value)}
                />
            </Modal>

            <Modal
                {...modalProps}
                open={isCopyTokenModalVisible}
                title={t('tokensTable.modal.copy.title')}
                onCancel={() => setIsCopyTokenModalVisible(false)}
            >
                <Input
                    style={{marginTop: '20px'}}
                    name="manuallyCopyToken"
                    value={manuallyCopyToken}
                    autoFocus
                    onFocus={(event) => event.target.select()}
                />
            </Modal>

            <EditTokenFormModal
                editingTokenId={editingTokenId}
                setEditingTokenId={setEditingTokenId}
                isTokenFormModalVisible={isTokenEditModalVisible}
                setIsTokenFormModalVisible={setIsTokenEditModalVisible}
                reload={() => safeAction('reload')}
            />

            <CodeExampleModal
                isCodeExampleModalVisible={isCodeExampleModalVisible}
                setIsCodeExampleModalVisible={setIsCodeExampleModalVisible}
                apiToken={apiToken}
                serverAddress={statusState.status.server_address}
                availableModels={codeExampleAvailableModels}
            />

            <RefreshTokenModal
                isRefreshTokenConfirmModalVisible={isRefreshTokenConfirmModalVisible}
                setIsRefreshTokenConfirmModalVisible={setIsRefreshTokenConfirmModalVisible}
                setRefreshTokenText={setRefreshTokenText}
                refreshTokenText={refreshTokenText}
                manageToken={manageToken}
                defaultTokenRecordId={defaultTokenRecordId}
            />

            <AddQuotaModal
                isModalAddQuotaVisible={isModalAddQuotaVisible}
                setIsModalAddQuotaVisible={setIsModalAddQuotaVisible}
                editingUserId={editingUserIdForBalance}
                setEditingUserId={setEditingUserIdForBalance}
                isQuotaExpireEnabled={statusState.status.QuotaExpireEnabled}
                reload={() => safeAction('reload')}
            />
        </>
    )
}

export default TokensTable
